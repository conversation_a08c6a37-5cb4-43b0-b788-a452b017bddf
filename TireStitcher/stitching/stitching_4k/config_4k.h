#ifndef CONFIG_4K_H
#define CONFIG_4K_H

#include "../common/base_config.h"
#include <vector>

/**
 * 4K-specific stitching configuration.
 * Optimized parameters for 4K video processing with focus on speed and efficiency.
 */
struct StitchConfig4K : public BaseStitchConfig {
    // 4K-specific movement detection parameters
    int templateMatchPrecision;
    double correlationThresholdLow;
    double correlationThresholdHigh;

    // 4K-specific strip processing parameters
    int stripWidth;
    double stripMarginPercent;
    double initialStripMarginPercent;

    // 4K-specific overlap and blending parameters
    int minOverlap;
    int maxOverlap;
    double overlapScaleFactor;
    double highCorrelationThreshold;

    // 4K-specific template matching parameters
    std::vector<double> templateSizes;
    std::vector<double> templatePositions;
    double templateQualityThreshold;

    // 4K-specific optical flow parameters
    double opticalFlowScale;
    std::vector<double> multiScaleFactors;
    double movementConfidenceThreshold;

    StitchConfig4K() : BaseStitchConfig() {
        resolutionMode = "4K";

        // 4K-optimized movement detection
        templateMatchPrecision = 1;  // Lower precision for speed
        correlationThresholdLow = 0.70;   // Lower threshold for 4K
        correlationThresholdHigh = 0.85;  // Lower high threshold for 4K

        // 4K-optimized strip processing
        stripWidth = 20;  // Default strip width for 4K
        stripMarginPercent = 0.15;  // 15% margin for strip width calculation
        initialStripMarginPercent = 0.10;  // 10% margin for initial strip

        // 4K-optimized overlap settings
        minOverlap = 3;  // Smaller minimum overlap for 4K
        maxOverlap = 15;  // Smaller maximum overlap for 4K
        overlapScaleFactor = 800.0;  // Scale factor for overlap calculation (width/800)
        highCorrelationThreshold = 0.90;  // Threshold for reduced overlap

        // 4K-optimized template matching
        templateSizes = {0.05, 0.08, 0.12, 0.18};  // Smaller templates for 4K
        templatePositions = {0.2, 0.35, 0.5, 0.65, 0.8};  // Fewer positions for speed
        templateQualityThreshold = 0.03;  // Lower quality threshold

        // 4K-optimized optical flow
        opticalFlowScale = 0.3;  // Larger scale for faster processing
        multiScaleFactors = {0.2, 0.3};  // Fewer scales for speed
        movementConfidenceThreshold = 0.75;  // Lower confidence threshold
    }
};

#endif // CONFIG_4K_H
