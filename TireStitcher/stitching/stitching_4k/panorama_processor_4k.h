#ifndef PANORAMA_PROCESSOR_4K_H
#define PANORAMA_PROCESSOR_4K_H

#include "config_4k.h"
#include "movement_detection_4k.h"
#include "image_blending_4k.h"
#include "../image_loader.h"
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>
#include <functional>

namespace Stitching4K {

/**
 * 4K-optimized panorama processor with focus on speed and efficiency.
 * Uses simplified algorithms and reduced precision for faster processing.
 */

/**
 * Main 4K stitching function using optical flow.
 * Optimized for speed with reduced quality requirements.
 */
cv::Mat stitchTireSurfaceWithOpticalFlow(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig4K& config,
    const std::string& serialNumber,
    const std::string& subprojectType
);

/**
 * 4K hierarchical stitching function.
 * Simplified hierarchical approach for faster processing.
 */
cv::Mat stitchTireSurfaceHierarchical(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig4K& config,
    const std::string& serialNumber,
    const std::string& subprojectType
);

/**
 * 4K-specific image pair stitching.
 * Simplified stitching for two images.
 */
cv::Mat stitchImagePair4K(
    const cv::Mat& img1,
    const cv::Mat& img2,
    bool useBlending,
    const StitchConfig4K& config
);

/**
 * Save intermediate panorama for 4K processing.
 * Simplified saving with reduced quality checks.
 */
void saveIntermediatePanorama4K(
    const cv::Mat& panorama,
    const std::string& outputFolder,
    int frameIdx,
    const StitchConfig4K& config
);

/**
 * Save final panorama versions for 4K processing.
 * Creates standard and enhanced versions with 4K-optimized settings.
 */
void savePanoramaVersions4K(
    const cv::Mat& panorama,
    const std::string& outputFolder,
    const StitchConfig4K& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
);

/**
 * 4K-specific strip extraction.
 * Simplified strip extraction for faster processing.
 */
cv::Mat getCentralStrip4K(
    const cv::Mat& image,
    int stripWidth,
    const StitchConfig4K& config
);

/**
 * Calculate strip width for 4K processing.
 * Uses simplified calculation based on movement and configuration.
 */
int calculateStripWidth4K(
    double movement,
    const StitchConfig4K& config,
    bool isFirstStrip = false
);

/**
 * 4K-specific movement validation.
 * Simplified validation with relaxed thresholds.
 */
bool isValidMovement4K(
    double movement,
    double correlation,
    const StitchConfig4K& config
);

/**
 * Process frame range for 4K stitching.
 * Simplified processing with reduced error checking.
 */
cv::Mat processFrameRange4K(
    const std::vector<std::string>& imagePaths,
    int startIdx,
    int endIdx,
    bool useBlending,
    const StitchConfig4K& config
);

} // namespace Stitching4K

#endif // PANORAMA_PROCESSOR_4K_H
