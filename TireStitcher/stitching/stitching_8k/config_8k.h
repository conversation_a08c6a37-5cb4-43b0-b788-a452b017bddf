#ifndef CONFIG_8K_H
#define CONFIG_8K_H

#include "../common/base_config.h"
#include <vector>

/**
 * 8K-specific stitching configuration.
 * Optimized parameters for 8K video processing with focus on quality and precision.
 * These parameters preserve the working 8K quality improvements mentioned in memories.
 */
struct StitchConfig8K : public BaseStitchConfig {
    // 8K-specific movement detection parameters
    int templateMatchPrecision;
    double correlationThresholdLow;
    double correlationThresholdHigh;

    // 8K-specific strip processing parameters
    int stripWidth;
    double stripMarginPercent;
    double initialStripMarginPercent;

    // 8K-specific overlap and blending parameters
    int minOverlap;
    int maxOverlap;
    double overlapScaleFactor;
    double highCorrelationThreshold;

    // 8K-specific template matching parameters
    std::vector<double> templateSizes;
    std::vector<double> templatePositions;
    double templateQualityThreshold;

    // 8K-specific optical flow parameters
    double opticalFlowScale;
    std::vector<double> multiScaleFactors;
    double movementConfidenceThreshold;

    // 8K-specific enhanced blending parameters
    bool useEnhancedBlending;
    double blendingFeatherPercent;
    int claheClipLimit;
    int claheTileSize;

    StitchConfig8K() : BaseStitchConfig() {
        resolutionMode = "8K";

        // 8K-optimized movement detection (preserve working quality)
        templateMatchPrecision = 2;  // Higher precision for quality
        correlationThresholdLow = 0.75;   // Working 8K threshold from memories
        correlationThresholdHigh = 0.92;  // Working 8K threshold from memories

        // 8K-optimized strip processing
        stripWidth = 0;  // Dynamic calculation for 8K
        stripMarginPercent = 0.10;  // 10% margin for strip width calculation
        initialStripMarginPercent = 0.10;  // 10% margin for initial strip

        // 8K-optimized overlap settings (enhanced blending with reduced minimum overlap)
        minOverlap = 5;  // Minimum overlap for 8K
        maxOverlap = 25;  // Larger maximum overlap for 8K quality
        overlapScaleFactor = 1000.0;  // Scale factor for overlap calculation (width/1000)
        highCorrelationThreshold = 0.95;  // Higher threshold for 8K quality

        // 8K-optimized template matching (more comprehensive for quality)
        templateSizes = {0.03, 0.05, 0.07, 0.11, 0.15, 0.2};  // Full range for quality
        templatePositions = {0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9};  // Full coverage
        templateQualityThreshold = 0.05;  // Higher quality threshold

        // 8K-optimized optical flow
        opticalFlowScale = 0.25;  // Smaller scale for precision
        multiScaleFactors = {0.15, 0.25, 0.4};  // Multiple scales for accuracy
        movementConfidenceThreshold = 0.80;  // Higher confidence threshold

        // 8K-specific enhanced blending (preserve working improvements)
        useEnhancedBlending = true;
        blendingFeatherPercent = 0.3;  // 30% feathering for smooth blends
        claheClipLimit = 2;  // CLAHE parameters for enhancement
        claheTileSize = 8;
    }
};

#endif // CONFIG_8K_H
